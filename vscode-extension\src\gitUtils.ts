import * as vscode from 'vscode';
import simpleGit, { SimpleGit, DiffResult } from 'simple-git';
import * as path from 'path';

export interface GitChange {
  filePath: string;
  changeType: 'ADD' | 'MODIFY' | 'DELETE';
  diff: string;
  insertions: number;
  deletions: number;
}

export interface GitCommitInfo {
  hash: string;
  author: string;
  email: string;
  date: Date;
  message: string;
}

export class GitUtils {
  private git: SimpleGit;
  private workspaceRoot: string;

  constructor(workspaceRoot: string) {
    this.workspaceRoot = workspaceRoot;
    this.git = simpleGit(workspaceRoot);
  }

  /**
   * Get the current Git user information
   */
  async getGitUser(): Promise<{ name: string; email: string }> {
    try {
      const name = await this.git.getConfig('user.name');
      const email = await this.git.getConfig('user.email');
      return {
        name: name.value || 'Unknown',
        email: email.value || '<EMAIL>'
      };
    } catch (error) {
      console.warn('Failed to get Git user info:', error);
      return {
        name: 'Unknown',
        email: '<EMAIL>'
      };
    }
  }

  /**
   * Get staged changes (ready to be committed)
   */
  async getStagedChanges(): Promise<GitChange[]> {
    try {
      const status = await this.git.status();
      const changes: GitChange[] = [];

      // Process staged files
      for (const file of status.staged) {
        const changeType = this.getChangeType(file);
        const diff = await this.getDiffForFile(file, true);
        
        changes.push({
          filePath: file,
          changeType,
          diff,
          insertions: 0, // Will be calculated from diff
          deletions: 0   // Will be calculated from diff
        });
      }

      return changes;
    } catch (error) {
      console.error('Failed to get staged changes:', error);
      return [];
    }
  }

  /**
   * Get unstaged changes (modified but not staged)
   */
  async getUnstagedChanges(): Promise<GitChange[]> {
    try {
      const status = await this.git.status();
      const changes: GitChange[] = [];

      // Process modified files
      for (const file of status.modified) {
        const diff = await this.getDiffForFile(file, false);
        
        changes.push({
          filePath: file,
          changeType: 'MODIFY',
          diff,
          insertions: 0,
          deletions: 0
        });
      }

      return changes;
    } catch (error) {
      console.error('Failed to get unstaged changes:', error);
      return [];
    }
  }

  /**
   * Get diff for a specific file
   */
  async getDiffForFile(filePath: string, staged: boolean = false): Promise<string> {
    try {
      const options = staged ? ['--cached'] : [];
      const diff = await this.git.diff([...options, '--', filePath]);
      return diff;
    } catch (error) {
      console.error(`Failed to get diff for ${filePath}:`, error);
      return '';
    }
  }

  /**
   * Get the current commit hash
   */
  async getCurrentCommitHash(): Promise<string | undefined> {
    try {
      const log = await this.git.log({ maxCount: 1 });
      return log.latest?.hash;
    } catch (error) {
      console.error('Failed to get current commit hash:', error);
      return undefined;
    }
  }

  /**
   * Get commit information
   */
  async getCommitInfo(hash?: string): Promise<GitCommitInfo | undefined> {
    try {
      const log = await this.git.log({ 
        maxCount: 1, 
        from: hash 
      });
      
      const commit = log.latest;
      if (!commit) {
        return undefined;
      }

      return {
        hash: commit.hash,
        author: commit.author_name,
        email: commit.author_email,
        date: new Date(commit.date),
        message: commit.message
      };
    } catch (error) {
      console.error('Failed to get commit info:', error);
      return undefined;
    }
  }

  /**
   * Check if the current directory is a Git repository
   */
  async isGitRepository(): Promise<boolean> {
    try {
      await this.git.status();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get the repository root path
   */
  async getRepositoryRoot(): Promise<string | undefined> {
    try {
      const root = await this.git.revparse(['--show-toplevel']);
      return root.trim();
    } catch (error) {
      console.error('Failed to get repository root:', error);
      return undefined;
    }
  }

  /**
   * Get file history for a specific file
   */
  async getFileHistory(filePath: string, maxCount: number = 10): Promise<GitCommitInfo[]> {
    try {
      const log = await this.git.log({
        file: filePath,
        maxCount
      });

      return log.all.map(commit => ({
        hash: commit.hash,
        author: commit.author_name,
        email: commit.author_email,
        date: new Date(commit.date),
        message: commit.message
      }));
    } catch (error) {
      console.error(`Failed to get file history for ${filePath}:`, error);
      return [];
    }
  }

  /**
   * Get changes between two commits
   */
  async getChangesBetweenCommits(fromCommit: string, toCommit: string): Promise<GitChange[]> {
    try {
      const diff = await this.git.diffSummary([fromCommit, toCommit]);
      const changes: GitChange[] = [];

      for (const file of diff.files) {
        const fileDiff = await this.git.diff([fromCommit, toCommit, '--', file.file]);
        
        changes.push({
          filePath: file.file,
          changeType: this.getChangeTypeFromDiff(file),
          diff: fileDiff,
          insertions: file.insertions,
          deletions: file.deletions
        });
      }

      return changes;
    } catch (error) {
      console.error('Failed to get changes between commits:', error);
      return [];
    }
  }

  /**
   * Check if a file should be excluded from diary recording
   */
  shouldExcludeFile(filePath: string, excludePatterns: string[]): boolean {
    const minimatch = require('minimatch');
    
    return excludePatterns.some(pattern => 
      minimatch(filePath, pattern, { dot: true })
    );
  }

  private getChangeType(filePath: string): 'ADD' | 'MODIFY' | 'DELETE' {
    // This is a simplified implementation
    // In a real scenario, you'd check the Git status more carefully
    return 'MODIFY';
  }

  private getChangeTypeFromDiff(file: any): 'ADD' | 'MODIFY' | 'DELETE' {
    if (file.insertions > 0 && file.deletions === 0) {
      return 'ADD';
    } else if (file.insertions === 0 && file.deletions > 0) {
      return 'DELETE';
    } else {
      return 'MODIFY';
    }
  }

  /**
   * Get relative path from workspace root
   */
  getRelativePath(absolutePath: string): string {
    return path.relative(this.workspaceRoot, absolutePath);
  }

  /**
   * Parse diff to extract line numbers and function/class context
   */
  parseDiffContext(diff: string): {
    lineNumbers: number[];
    functions: string[];
    classes: string[];
  } {
    const lineNumbers: number[] = [];
    const functions: string[] = [];
    const classes: string[] = [];

    const lines = diff.split('\n');
    let currentLineNumber = 0;

    for (const line of lines) {
      // Parse hunk headers to get line numbers
      const hunkMatch = line.match(/^@@\s+-\d+(?:,\d+)?\s+\+(\d+)(?:,\d+)?\s+@@/);
      if (hunkMatch) {
        currentLineNumber = parseInt(hunkMatch[1], 10);
        continue;
      }

      // Track line numbers for added/modified lines
      if (line.startsWith('+') && !line.startsWith('+++')) {
        lineNumbers.push(currentLineNumber);
      }

      if (!line.startsWith('-')) {
        currentLineNumber++;
      }

      // Extract function and class names (basic patterns)
      const functionMatch = line.match(/[+-]\s*(?:function|def|fn)\s+(\w+)/);
      if (functionMatch) {
        functions.push(functionMatch[1]);
      }

      const classMatch = line.match(/[+-]\s*(?:class|interface|struct)\s+(\w+)/);
      if (classMatch) {
        classes.push(classMatch[1]);
      }
    }

    return {
      lineNumbers: [...new Set(lineNumbers)],
      functions: [...new Set(functions)],
      classes: [...new Set(classes)]
    };
  }
}
