import { Tool } from '@modelcontextprotocol/sdk/types.js';

export const TOOLS: Tool[] = [
  {
    name: 'create_diary_entry',
    description: 'Create a new diary entry for a code change',
    inputSchema: {
      type: 'object',
      properties: {
        entry: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: 'ISO timestamp of when the change was made'
            },
            filePath: {
              type: 'string',
              description: 'Path to the file that was changed'
            },
            commitHash: {
              type: 'string',
              description: 'Git commit hash (optional)'
            },
            author: {
              type: 'string',
              description: 'Author of the change'
            },
            changeType: {
              type: 'string',
              enum: ['ADD', 'MODIFY', 'DELETE', 'REFACTOR', 'FIX', 'FEATURE'],
              description: 'Type of change made'
            },
            codeDiff: {
              type: 'string',
              description: 'The actual code diff'
            },
            developerRationale: {
              type: 'string',
              description: 'Developer explanation of why the change was made'
            },
            aiSummary: {
              type: 'string',
              description: 'AI-generated summary (optional)'
            },
            aiImpactAnalysis: {
              type: 'string',
              description: 'AI-generated impact analysis (optional)'
            },
            tags: {
              type: 'array',
              items: { type: 'string' },
              description: 'Tags for categorization'
            },
            lineNumber: {
              type: 'number',
              description: 'Specific line number affected (optional)'
            },
            functionName: {
              type: 'string',
              description: 'Function name affected (optional)'
            },
            className: {
              type: 'string',
              description: 'Class name affected (optional)'
            }
          },
          required: ['timestamp', 'filePath', 'author', 'changeType', 'codeDiff', 'developerRationale']
        }
      },
      required: ['entry']
    }
  },
  {
    name: 'search_diary_entries',
    description: 'Search diary entries with various filters',
    inputSchema: {
      type: 'object',
      properties: {
        query: {
          type: 'object',
          properties: {
            filePath: {
              type: 'string',
              description: 'Filter by file path (supports partial matches)'
            },
            author: {
              type: 'string',
              description: 'Filter by author'
            },
            changeType: {
              type: 'string',
              description: 'Filter by change type'
            },
            keyword: {
              type: 'string',
              description: 'Search in rationale, summary, and diff'
            },
            commitHash: {
              type: 'string',
              description: 'Filter by commit hash'
            },
            dateFrom: {
              type: 'string',
              description: 'Start date (ISO format)'
            },
            dateTo: {
              type: 'string',
              description: 'End date (ISO format)'
            },
            lineNumber: {
              type: 'number',
              description: 'Filter by line number'
            },
            limit: {
              type: 'number',
              default: 50,
              description: 'Maximum number of results'
            },
            offset: {
              type: 'number',
              default: 0,
              description: 'Offset for pagination'
            }
          }
        }
      },
      required: ['query']
    }
  },
  {
    name: 'get_file_history',
    description: 'Get the change history for a specific file',
    inputSchema: {
      type: 'object',
      properties: {
        filePath: {
          type: 'string',
          description: 'Path to the file'
        },
        lineNumber: {
          type: 'number',
          description: 'Specific line number (optional)'
        }
      },
      required: ['filePath']
    }
  },
  {
    name: 'get_commit_entries',
    description: 'Get all diary entries for a specific commit',
    inputSchema: {
      type: 'object',
      properties: {
        commitHash: {
          type: 'string',
          description: 'Git commit hash'
        }
      },
      required: ['commitHash']
    }
  },
  {
    name: 'search_by_intent',
    description: 'Search diary entries by developer intent or purpose',
    inputSchema: {
      type: 'object',
      properties: {
        intent: {
          type: 'string',
          description: 'The intent or purpose to search for (e.g., "fix authentication bug")'
        },
        limit: {
          type: 'number',
          default: 10,
          description: 'Maximum number of results'
        }
      },
      required: ['intent']
    }
  },
  {
    name: 'get_evolution',
    description: 'Get the evolution timeline of a file, function, or class',
    inputSchema: {
      type: 'object',
      properties: {
        filePath: {
          type: 'string',
          description: 'Path to the file'
        },
        functionName: {
          type: 'string',
          description: 'Specific function name (optional)'
        },
        className: {
          type: 'string',
          description: 'Specific class name (optional)'
        }
      },
      required: ['filePath']
    }
  },
  {
    name: 'get_diary_entry',
    description: 'Get a specific diary entry by ID',
    inputSchema: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'Diary entry ID'
        }
      },
      required: ['id']
    }
  },
  {
    name: 'correlate_changes',
    description: 'Find related changes made around the same time',
    inputSchema: {
      type: 'object',
      properties: {
        timestamp: {
          type: 'string',
          description: 'Reference timestamp (ISO format)'
        },
        timeWindow: {
          type: 'number',
          default: 3600,
          description: 'Time window in seconds (default: 1 hour)'
        },
        excludeAuthor: {
          type: 'string',
          description: 'Exclude changes by this author (optional)'
        }
      },
      required: ['timestamp']
    }
  }
];
