import axios, { AxiosInstance } from 'axios';
import * as vscode from 'vscode';

export interface DiaryEntry {
  id?: string;
  timestamp: string;
  filePath: string;
  commitHash?: string;
  author: string;
  changeType: 'ADD' | 'MODIFY' | 'DELETE' | 'REFACTOR' | 'FIX' | 'FEATURE';
  codeDiff: string;
  developerRationale: string;
  aiSummary?: string;
  aiImpactAnalysis?: string;
  tags?: string[];
  lineNumber?: number;
  functionName?: string;
  className?: string;
}

export interface SearchQuery {
  filePath?: string;
  author?: string;
  changeType?: string;
  keyword?: string;
  commitHash?: string;
  dateFrom?: string;
  dateTo?: string;
  lineNumber?: number;
  limit?: number;
  offset?: number;
}

export interface SearchResult {
  entries: DiaryEntry[];
  total: number;
  hasMore: boolean;
}

export class MCPClient {
  private serverProcess: any;
  private isServerRunning: boolean = false;

  constructor() {
    // In a real implementation, this would establish a connection to the MCP server
    // For now, we'll simulate the connection
  }

  /**
   * Start the MCP server process
   */
  async startServer(): Promise<void> {
    try {
      const config = vscode.workspace.getConfiguration('codeDiary');
      const serverPath = config.get<string>('mcpServerPath');
      const databasePath = config.get<string>('databasePath', './diary.db');

      if (!serverPath) {
        throw new Error('MCP server path not configured. Please set codeDiary.mcpServerPath in settings.');
      }

      // In a real implementation, you would spawn the MCP server process here
      // For now, we'll simulate it
      console.log(`Starting MCP server: ${serverPath} --db-path ${databasePath}`);
      
      this.isServerRunning = true;
      vscode.window.showInformationMessage('Code Diary MCP server started');
    } catch (error) {
      console.error('Failed to start MCP server:', error);
      vscode.window.showErrorMessage(`Failed to start MCP server: ${error}`);
      throw error;
    }
  }

  /**
   * Stop the MCP server process
   */
  async stopServer(): Promise<void> {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
    this.isServerRunning = false;
    console.log('MCP server stopped');
  }

  /**
   * Check if the server is running
   */
  isRunning(): boolean {
    return this.isServerRunning;
  }

  /**
   * Create a new diary entry
   */
  async createEntry(entry: DiaryEntry): Promise<DiaryEntry> {
    if (!this.isServerRunning) {
      throw new Error('MCP server is not running');
    }

    try {
      // In a real implementation, this would send an MCP request
      // For now, we'll simulate the response
      const createdEntry: DiaryEntry = {
        ...entry,
        id: `diary_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: entry.timestamp || new Date().toISOString()
      };

      console.log('Created diary entry:', createdEntry);
      return createdEntry;
    } catch (error) {
      console.error('Failed to create diary entry:', error);
      throw new Error(`Failed to create diary entry: ${error}`);
    }
  }

  /**
   * Search diary entries
   */
  async searchEntries(query: SearchQuery): Promise<SearchResult> {
    if (!this.isServerRunning) {
      throw new Error('MCP server is not running');
    }

    try {
      // In a real implementation, this would send an MCP request
      // For now, we'll simulate the response
      const mockEntries: DiaryEntry[] = [
        {
          id: 'mock_1',
          timestamp: new Date().toISOString(),
          filePath: query.filePath || 'src/example.ts',
          author: 'John Doe',
          changeType: 'FIX',
          codeDiff: '- if (user.isValid) {\n+ if (user && user.isValid) {',
          developerRationale: 'Added null check to prevent runtime error',
          tags: ['bugfix']
        }
      ];

      return {
        entries: mockEntries,
        total: mockEntries.length,
        hasMore: false
      };
    } catch (error) {
      console.error('Failed to search diary entries:', error);
      throw new Error(`Failed to search diary entries: ${error}`);
    }
  }

  /**
   * Get file history
   */
  async getFileHistory(filePath: string, lineNumber?: number): Promise<DiaryEntry[]> {
    if (!this.isServerRunning) {
      throw new Error('MCP server is not running');
    }

    try {
      // In a real implementation, this would send an MCP request
      const mockEntries: DiaryEntry[] = [
        {
          id: 'history_1',
          timestamp: new Date().toISOString(),
          filePath,
          author: 'Jane Smith',
          changeType: 'FEATURE',
          codeDiff: '+ function newFeature() {\n+   return true;\n+ }',
          developerRationale: 'Added new feature for user authentication',
          lineNumber,
          tags: ['feature', 'auth']
        }
      ];

      return mockEntries;
    } catch (error) {
      console.error('Failed to get file history:', error);
      throw new Error(`Failed to get file history: ${error}`);
    }
  }

  /**
   * Get commit entries
   */
  async getCommitEntries(commitHash: string): Promise<DiaryEntry[]> {
    if (!this.isServerRunning) {
      throw new Error('MCP server is not running');
    }

    try {
      // In a real implementation, this would send an MCP request
      const mockEntries: DiaryEntry[] = [
        {
          id: 'commit_1',
          timestamp: new Date().toISOString(),
          filePath: 'src/main.ts',
          commitHash,
          author: 'Bob Wilson',
          changeType: 'REFACTOR',
          codeDiff: '- const result = oldFunction();\n+ const result = newFunction();',
          developerRationale: 'Refactored to use new API',
          tags: ['refactor', 'api']
        }
      ];

      return mockEntries;
    } catch (error) {
      console.error('Failed to get commit entries:', error);
      throw new Error(`Failed to get commit entries: ${error}`);
    }
  }

  /**
   * Search by intent
   */
  async searchByIntent(intent: string, limit: number = 10): Promise<DiaryEntry[]> {
    if (!this.isServerRunning) {
      throw new Error('MCP server is not running');
    }

    try {
      // In a real implementation, this would send an MCP request
      const mockEntries: DiaryEntry[] = [
        {
          id: 'intent_1',
          timestamp: new Date().toISOString(),
          filePath: 'src/auth.ts',
          author: 'Alice Johnson',
          changeType: 'FIX',
          codeDiff: '- password.length > 0\n+ password.length >= 8',
          developerRationale: `Fixed password validation to match intent: ${intent}`,
          tags: ['security', 'validation']
        }
      ];

      return mockEntries.slice(0, limit);
    } catch (error) {
      console.error('Failed to search by intent:', error);
      throw new Error(`Failed to search by intent: ${error}`);
    }
  }

  /**
   * Get evolution of a file/function/class
   */
  async getEvolution(filePath: string, functionName?: string, className?: string): Promise<{
    entries: DiaryEntry[];
    timeline: Array<{
      date: string;
      changeCount: number;
      majorChanges: string[];
    }>;
  }> {
    if (!this.isServerRunning) {
      throw new Error('MCP server is not running');
    }

    try {
      // In a real implementation, this would send an MCP request
      const mockEntries: DiaryEntry[] = [
        {
          id: 'evolution_1',
          timestamp: '2024-01-01T10:00:00Z',
          filePath,
          author: 'Developer 1',
          changeType: 'ADD',
          codeDiff: '+ function initialImplementation() {}',
          developerRationale: 'Initial implementation',
          functionName,
          className,
          tags: ['initial']
        },
        {
          id: 'evolution_2',
          timestamp: '2024-01-15T14:30:00Z',
          filePath,
          author: 'Developer 2',
          changeType: 'REFACTOR',
          codeDiff: '- function initialImplementation() {}\n+ function improvedImplementation() {}',
          developerRationale: 'Improved implementation for better performance',
          functionName,
          className,
          tags: ['performance', 'refactor']
        }
      ];

      const timeline = [
        {
          date: '2024-01-01',
          changeCount: 1,
          majorChanges: ['ADD: Initial implementation']
        },
        {
          date: '2024-01-15',
          changeCount: 1,
          majorChanges: ['REFACTOR: Improved implementation for better performance']
        }
      ];

      return { entries: mockEntries, timeline };
    } catch (error) {
      console.error('Failed to get evolution:', error);
      throw new Error(`Failed to get evolution: ${error}`);
    }
  }

  /**
   * Correlate changes made around the same time
   */
  async correlateChanges(
    timestamp: string, 
    timeWindow: number = 3600, 
    excludeAuthor?: string
  ): Promise<DiaryEntry[]> {
    if (!this.isServerRunning) {
      throw new Error('MCP server is not running');
    }

    try {
      // In a real implementation, this would send an MCP request
      const mockEntries: DiaryEntry[] = [
        {
          id: 'correlated_1',
          timestamp: new Date(Date.parse(timestamp) + 1800000).toISOString(), // 30 minutes later
          filePath: 'src/related.ts',
          author: 'Related Developer',
          changeType: 'MODIFY',
          codeDiff: '- oldValue\n+ newValue',
          developerRationale: 'Updated to work with recent changes',
          tags: ['related']
        }
      ];

      return excludeAuthor 
        ? mockEntries.filter(entry => entry.author !== excludeAuthor)
        : mockEntries;
    } catch (error) {
      console.error('Failed to correlate changes:', error);
      throw new Error(`Failed to correlate changes: ${error}`);
    }
  }
}
