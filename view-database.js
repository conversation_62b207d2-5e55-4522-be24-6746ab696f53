#!/usr/bin/env node

// Simple script to view the contents of your Code Diary database

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'diary.db');

console.log('📊 Code Diary Database Contents\n');
console.log(`Database location: ${dbPath}\n`);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    return;
  }
  console.log('✅ Connected to the SQLite database.\n');
});

// Get all entries
db.all("SELECT * FROM diary_entries ORDER BY timestamp DESC", [], (err, rows) => {
  if (err) {
    console.error('❌ Error querying database:', err.message);
    return;
  }

  console.log(`📝 Found ${rows.length} diary entries:\n`);

  rows.forEach((row, index) => {
    console.log(`Entry ${index + 1}:`);
    console.log(`  ID: ${row.id}`);
    console.log(`  File: ${row.filePath}`);
    console.log(`  Author: ${row.author}`);
    console.log(`  Type: ${row.changeType}`);
    console.log(`  Time: ${row.timestamp}`);
    console.log(`  Function: ${row.functionName || 'N/A'}`);
    console.log(`  Line: ${row.lineNumber || 'N/A'}`);
    console.log(`  Tags: ${row.tags || 'None'}`);
    console.log(`  Rationale: ${row.developerRationale}`);
    if (row.aiSummary) {
      console.log(`  AI Summary: ${row.aiSummary}`);
    }
    if (row.aiImpactAnalysis) {
      console.log(`  AI Impact: ${row.aiImpactAnalysis}`);
    }
    console.log(`  Code Diff:`);
    console.log(`    ${row.codeDiff.replace(/\n/g, '\n    ')}`);
    console.log('  ' + '─'.repeat(50));
    console.log('');
  });

  // Get some statistics
  db.get("SELECT COUNT(*) as total FROM diary_entries", [], (err, row) => {
    if (!err) {
      console.log(`📊 Database Statistics:`);
      console.log(`  Total entries: ${row.total}`);
    }
  });

  db.all("SELECT changeType, COUNT(*) as count FROM diary_entries GROUP BY changeType", [], (err, rows) => {
    if (!err && rows.length > 0) {
      console.log(`  Change types:`);
      rows.forEach(row => {
        console.log(`    ${row.changeType}: ${row.count}`);
      });
    }
  });

  db.all("SELECT author, COUNT(*) as count FROM diary_entries GROUP BY author", [], (err, rows) => {
    if (!err && rows.length > 0) {
      console.log(`  Authors: <AUTHORS>
      rows.forEach(row => {
        console.log(`    ${row.author}: ${row.count} entries`);
      });
    }
    
    console.log('\n✅ Database query complete.');
    db.close();
  });
});
