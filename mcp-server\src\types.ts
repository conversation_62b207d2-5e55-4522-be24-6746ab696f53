import { z } from 'zod';

// Zod schemas for validation
export const DiaryEntrySchema = z.object({
  id: z.string().optional(),
  timestamp: z.string(),
  filePath: z.string(),
  commitHash: z.string().optional(),
  author: z.string(),
  changeType: z.enum(['ADD', 'MODIFY', 'DELETE', 'REFACTOR', 'FIX', 'FEATURE']),
  codeDiff: z.string(),
  developerRationale: z.string(),
  aiSummary: z.string().optional(),
  aiImpactAnalysis: z.string().optional(),
  tags: z.array(z.string()).default([]),
  lineNumber: z.number().optional(),
  functionName: z.string().optional(),
  className: z.string().optional(),

  // Enhanced context fields for tremendous value
  businessContext: z.string().optional(), // Why this change matters to the business
  technicalDebt: z.string().optional(), // Trade-offs made, debt incurred
  alternativesConsidered: z.string().optional(), // Other approaches considered and why rejected
  futureConsiderations: z.string().optional(), // What to watch out for, future improvements
  relatedTickets: z.array(z.string()).default([]), // JIRA/GitHub issues
  reviewComments: z.string().optional(), // Key insights from code review
  performanceImpact: z.string().optional(), // Performance considerations
  securityImpact: z.string().optional(), // Security implications
  testingStrategy: z.string().optional(), // How this was tested
  rollbackPlan: z.string().optional(), // How to undo if needed
  dependencies: z.array(z.string()).default([]), // What this depends on
  affectedComponents: z.array(z.string()).default([]), // What this affects
  estimatedComplexity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
  urgencyLevel: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
  knowledgeLevel: z.enum(['JUNIOR', 'MID', 'SENIOR', 'EXPERT']).optional() // Author's expertise level
});

export const SearchQuerySchema = z.object({
  filePath: z.string().optional(),
  author: z.string().optional(),
  changeType: z.string().optional(),
  keyword: z.string().optional(),
  commitHash: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  lineNumber: z.number().optional(),
  limit: z.number().default(50),
  offset: z.number().default(0)
});

// TypeScript types derived from schemas
export type DiaryEntry = z.infer<typeof DiaryEntrySchema>;
export type SearchQuery = z.infer<typeof SearchQuerySchema>;

// Database row type (includes auto-generated fields)
export interface DiaryEntryRow extends DiaryEntry {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// MCP Tool parameter types
export interface CreateEntryParams {
  entry: DiaryEntry;
}

export interface SearchEntriesParams {
  query: SearchQuery;
}

export interface GetEntryParams {
  id: string;
}

export interface GetFileHistoryParams {
  filePath: string;
  lineNumber?: number;
}

export interface GetCommitEntriesParams {
  commitHash: string;
}

export interface SearchByIntentParams {
  intent: string;
  limit?: number;
}

export interface GetEvolutionParams {
  filePath: string;
  functionName?: string;
  className?: string;
}

// Response types
export interface SearchResult {
  entries: DiaryEntryRow[];
  total: number;
  hasMore: boolean;
}

export interface EvolutionResult {
  entries: DiaryEntryRow[];
  timeline: {
    date: string;
    changeCount: number;
    majorChanges: string[];
  }[];
}

// Error types
export class DiaryError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message);
    this.name = 'DiaryError';
  }
}

export class ValidationError extends DiaryError {
  constructor(message: string) {
    super(message, 'VALIDATION_ERROR', 400);
  }
}

export class NotFoundError extends DiaryError {
  constructor(message: string) {
    super(message, 'NOT_FOUND', 404);
  }
}

export class DatabaseError extends DiaryError {
  constructor(message: string) {
    super(message, 'DATABASE_ERROR', 500);
  }
}
