import { z } from 'zod';
export declare const DiaryEntrySchema: z.ZodObject<{
    id: z.ZodOptional<z.ZodString>;
    timestamp: z.ZodString;
    filePath: z.ZodString;
    commitHash: z.ZodOptional<z.ZodString>;
    author: z.ZodString;
    changeType: z.ZodE<PERSON><["ADD", "MODIFY", "DELETE", "REFACTOR", "FIX", "FEATURE"]>;
    codeDiff: z.ZodString;
    developerRationale: z.ZodString;
    aiSummary: z.ZodOptional<z.ZodString>;
    aiImpactAnalysis: z.ZodOptional<z.ZodString>;
    tags: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    lineNumber: z.ZodOptional<z.ZodNumber>;
    functionName: z.ZodOptional<z.ZodString>;
    className: z.ZodOptional<z.ZodString>;
    businessContext: z.<PERSON>ptional<z.ZodString>;
    technicalDebt: z.<PERSON><z.ZodString>;
    alternativesConsidered: z.<PERSON>ptional<z.ZodString>;
    futureConsiderations: z.ZodOptional<z.ZodString>;
    relatedTickets: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    reviewComments: z.ZodOptional<z.ZodString>;
    performanceImpact: z.ZodOptional<z.ZodString>;
    securityImpact: z.ZodOptional<z.ZodString>;
    testingStrategy: z.ZodOptional<z.ZodString>;
    rollbackPlan: z.ZodOptional<z.ZodString>;
    dependencies: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    affectedComponents: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    estimatedComplexity: z.ZodOptional<z.ZodEnum<["LOW", "MEDIUM", "HIGH", "CRITICAL"]>>;
    urgencyLevel: z.ZodOptional<z.ZodEnum<["LOW", "MEDIUM", "HIGH", "CRITICAL"]>>;
    knowledgeLevel: z.ZodOptional<z.ZodEnum<["JUNIOR", "MID", "SENIOR", "EXPERT"]>>;
}, "strip", z.ZodTypeAny, {
    timestamp: string;
    filePath: string;
    author: string;
    changeType: "ADD" | "MODIFY" | "DELETE" | "REFACTOR" | "FIX" | "FEATURE";
    codeDiff: string;
    developerRationale: string;
    tags: string[];
    relatedTickets: string[];
    dependencies: string[];
    affectedComponents: string[];
    id?: string | undefined;
    commitHash?: string | undefined;
    aiSummary?: string | undefined;
    aiImpactAnalysis?: string | undefined;
    lineNumber?: number | undefined;
    functionName?: string | undefined;
    className?: string | undefined;
    businessContext?: string | undefined;
    technicalDebt?: string | undefined;
    alternativesConsidered?: string | undefined;
    futureConsiderations?: string | undefined;
    reviewComments?: string | undefined;
    performanceImpact?: string | undefined;
    securityImpact?: string | undefined;
    testingStrategy?: string | undefined;
    rollbackPlan?: string | undefined;
    estimatedComplexity?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL" | undefined;
    urgencyLevel?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL" | undefined;
    knowledgeLevel?: "JUNIOR" | "MID" | "SENIOR" | "EXPERT" | undefined;
}, {
    timestamp: string;
    filePath: string;
    author: string;
    changeType: "ADD" | "MODIFY" | "DELETE" | "REFACTOR" | "FIX" | "FEATURE";
    codeDiff: string;
    developerRationale: string;
    id?: string | undefined;
    commitHash?: string | undefined;
    aiSummary?: string | undefined;
    aiImpactAnalysis?: string | undefined;
    tags?: string[] | undefined;
    lineNumber?: number | undefined;
    functionName?: string | undefined;
    className?: string | undefined;
    businessContext?: string | undefined;
    technicalDebt?: string | undefined;
    alternativesConsidered?: string | undefined;
    futureConsiderations?: string | undefined;
    relatedTickets?: string[] | undefined;
    reviewComments?: string | undefined;
    performanceImpact?: string | undefined;
    securityImpact?: string | undefined;
    testingStrategy?: string | undefined;
    rollbackPlan?: string | undefined;
    dependencies?: string[] | undefined;
    affectedComponents?: string[] | undefined;
    estimatedComplexity?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL" | undefined;
    urgencyLevel?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL" | undefined;
    knowledgeLevel?: "JUNIOR" | "MID" | "SENIOR" | "EXPERT" | undefined;
}>;
export declare const SearchQuerySchema: z.ZodObject<{
    filePath: z.ZodOptional<z.ZodString>;
    author: z.ZodOptional<z.ZodString>;
    changeType: z.ZodOptional<z.ZodString>;
    keyword: z.ZodOptional<z.ZodString>;
    commitHash: z.ZodOptional<z.ZodString>;
    dateFrom: z.ZodOptional<z.ZodString>;
    dateTo: z.ZodOptional<z.ZodString>;
    lineNumber: z.ZodOptional<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
    offset: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    limit: number;
    offset: number;
    filePath?: string | undefined;
    commitHash?: string | undefined;
    author?: string | undefined;
    changeType?: string | undefined;
    lineNumber?: number | undefined;
    keyword?: string | undefined;
    dateFrom?: string | undefined;
    dateTo?: string | undefined;
}, {
    filePath?: string | undefined;
    commitHash?: string | undefined;
    author?: string | undefined;
    changeType?: string | undefined;
    lineNumber?: number | undefined;
    keyword?: string | undefined;
    dateFrom?: string | undefined;
    dateTo?: string | undefined;
    limit?: number | undefined;
    offset?: number | undefined;
}>;
export type DiaryEntry = z.infer<typeof DiaryEntrySchema>;
export type SearchQuery = z.infer<typeof SearchQuerySchema>;
export interface DiaryEntryRow extends DiaryEntry {
    id: string;
    createdAt: string;
    updatedAt: string;
}
export interface CreateEntryParams {
    entry: DiaryEntry;
}
export interface SearchEntriesParams {
    query: SearchQuery;
}
export interface GetEntryParams {
    id: string;
}
export interface GetFileHistoryParams {
    filePath: string;
    lineNumber?: number;
}
export interface GetCommitEntriesParams {
    commitHash: string;
}
export interface SearchByIntentParams {
    intent: string;
    limit?: number;
}
export interface GetEvolutionParams {
    filePath: string;
    functionName?: string;
    className?: string;
}
export interface SearchResult {
    entries: DiaryEntryRow[];
    total: number;
    hasMore: boolean;
}
export interface EvolutionResult {
    entries: DiaryEntryRow[];
    timeline: {
        date: string;
        changeCount: number;
        majorChanges: string[];
    }[];
}
export declare class DiaryError extends Error {
    code: string;
    statusCode: number;
    constructor(message: string, code: string, statusCode?: number);
}
export declare class ValidationError extends DiaryError {
    constructor(message: string);
}
export declare class NotFoundError extends DiaryError {
    constructor(message: string);
}
export declare class DatabaseError extends DiaryError {
    constructor(message: string);
}
//# sourceMappingURL=types.d.ts.map