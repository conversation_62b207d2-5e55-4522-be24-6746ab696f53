#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Create a log file to track what's happening
const logFile = path.join(__dirname, 'debug.log');

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp}: ${message}\n`;
  fs.appendFileSync(logFile, logMessage);
  console.error(logMessage.trim()); // Use stderr so it doesn't interfere with MCP
}

try {
  log('Debug server starting...');
  
  // Set the working directory to the mcp-server folder
  const serverDir = __dirname;
  const serverScript = path.join(serverDir, 'dist', 'server.js');
  const dbPath = path.join(path.dirname(serverDir), 'diary.db');
  
  log(`Server dir: ${serverDir}`);
  log(`Server script: ${serverScript}`);
  log(`DB path: ${dbPath}`);
  
  // Check if files exist
  if (!fs.existsSync(serverScript)) {
    log(`ERROR: Server script not found at ${serverScript}`);
    process.exit(1);
  }
  
  log('Server script exists, starting...');
  
  // Start the MCP server
  const server = spawn('node', [serverScript, '--db-path', dbPath], {
    cwd: serverDir,
    stdio: ['inherit', 'inherit', 'pipe'] // Capture stderr for logging
  });
  
  server.stderr.on('data', (data) => {
    log(`Server stderr: ${data.toString()}`);
  });
  
  server.on('spawn', () => {
    log('Server process spawned successfully');
  });
  
  server.on('error', (error) => {
    log(`Server spawn error: ${error.message}`);
    process.exit(1);
  });
  
  // Handle server exit
  server.on('close', (code, signal) => {
    log(`Server closed with code ${code} and signal ${signal}`);
    process.exit(code);
  });
  
  // Handle process termination
  process.on('SIGINT', () => {
    log('Received SIGINT, killing server...');
    server.kill('SIGINT');
  });
  
  process.on('SIGTERM', () => {
    log('Received SIGTERM, killing server...');
    server.kill('SIGTERM');
  });
  
  log('Debug server setup complete');
  
} catch (error) {
  log(`Fatal error: ${error.message}`);
  process.exit(1);
}
