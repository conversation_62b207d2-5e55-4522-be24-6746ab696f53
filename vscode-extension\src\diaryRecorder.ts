import * as vscode from 'vscode';
import { GitUtils, GitChange } from './gitUtils';
import { MCPClient, DiaryEntry } from './mcpClient';

export interface RecordingOptions {
  promptForRationale?: boolean;
  includeContext?: boolean;
  autoDetectChangeType?: boolean;
}

export class DiaryRecorder {
  private gitUtils: GitUtils;
  private mcpClient: MCPClient;
  private disposables: vscode.Disposable[] = [];

  constructor(workspaceRoot: string, mcpClient: MCPClient) {
    this.gitUtils = new GitUtils(workspaceRoot);
    this.mcpClient = mcpClient;
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    // Listen for Git extension events if available
    const gitExtension = vscode.extensions.getExtension('vscode.git');
    if (gitExtension && gitExtension.isActive) {
      this.setupGitExtensionListeners(gitExtension.exports);
    }

    // Listen for file save events as a fallback
    this.disposables.push(
      vscode.workspace.onDidSaveTextDocument(this.onFileSaved.bind(this))
    );
  }

  private setupGitExtensionListeners(gitApi: any): void {
    try {
      // Get the first repository
      const repo = gitApi.repositories[0];
      if (repo) {
        // Listen for repository state changes
        this.disposables.push(
          repo.state.onDidChange(() => {
            this.onRepositoryStateChanged(repo);
          })
        );
      }
    } catch (error) {
      console.warn('Failed to setup Git extension listeners:', error);
    }
  }

  private async onRepositoryStateChanged(repo: any): void {
    const config = vscode.workspace.getConfiguration('codeDiary');
    const autoRecord = config.get<boolean>('autoRecordOnCommit', true);

    if (!autoRecord) {
      return;
    }

    // Check if there are staged changes ready for commit
    try {
      const stagedChanges = await this.gitUtils.getStagedChanges();
      if (stagedChanges.length > 0) {
        await this.promptForCommitRationale(stagedChanges);
      }
    } catch (error) {
      console.error('Error checking staged changes:', error);
    }
  }

  private async onFileSaved(document: vscode.TextDocument): void {
    const config = vscode.workspace.getConfiguration('codeDiary');
    const promptForMinor = config.get<boolean>('promptForMinorChanges', false);

    if (!promptForMinor) {
      return;
    }

    // Check if this file should be excluded
    const excludePatterns = config.get<string[]>('excludePatterns', []);
    const relativePath = this.gitUtils.getRelativePath(document.fileName);
    
    if (this.gitUtils.shouldExcludeFile(relativePath, excludePatterns)) {
      return;
    }

    // Get unstaged changes for this file
    try {
      const changes = await this.gitUtils.getUnstagedChanges();
      const fileChange = changes.find(change => change.filePath === relativePath);
      
      if (fileChange) {
        await this.promptForChangeRationale([fileChange]);
      }
    } catch (error) {
      console.error('Error checking file changes:', error);
    }
  }

  /**
   * Manually record a change for the current selection or file
   */
  async recordCurrentChange(): Promise<void> {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      vscode.window.showWarningMessage('No active editor found');
      return;
    }

    const document = editor.document;
    const relativePath = this.gitUtils.getRelativePath(document.fileName);

    try {
      // Get the diff for this file
      const diff = await this.gitUtils.getDiffForFile(relativePath, false);
      
      if (!diff) {
        vscode.window.showInformationMessage('No changes detected in current file');
        return;
      }

      const change: GitChange = {
        filePath: relativePath,
        changeType: 'MODIFY',
        diff,
        insertions: 0,
        deletions: 0
      };

      await this.promptForChangeRationale([change], {
        promptForRationale: true,
        includeContext: true,
        autoDetectChangeType: true
      });
    } catch (error) {
      console.error('Error recording current change:', error);
      vscode.window.showErrorMessage(`Failed to record change: ${error}`);
    }
  }

  /**
   * Prompt user for rationale when committing changes
   */
  private async promptForCommitRationale(changes: GitChange[]): Promise<void> {
    if (changes.length === 0) {
      return;
    }

    const changesSummary = this.summarizeChanges(changes);
    const message = `You're about to commit ${changes.length} file(s). Would you like to record the rationale for these changes?`;

    const action = await vscode.window.showInformationMessage(
      message,
      { modal: false },
      'Record Rationale',
      'Skip',
      'Don\'t Ask Again'
    );

    switch (action) {
      case 'Record Rationale':
        await this.promptForChangeRationale(changes, { promptForRationale: true });
        break;
      case 'Don\'t Ask Again':
        await vscode.workspace.getConfiguration('codeDiary').update(
          'autoRecordOnCommit',
          false,
          vscode.ConfigurationTarget.Workspace
        );
        break;
      // 'Skip' or undefined - do nothing
    }
  }

  /**
   * Prompt user for change rationale
   */
  private async promptForChangeRationale(
    changes: GitChange[], 
    options: RecordingOptions = {}
  ): Promise<void> {
    try {
      for (const change of changes) {
        await this.recordSingleChange(change, options);
      }
    } catch (error) {
      console.error('Error prompting for rationale:', error);
      vscode.window.showErrorMessage(`Failed to record rationale: ${error}`);
    }
  }

  /**
   * Record a single change with user input
   */
  private async recordSingleChange(
    change: GitChange, 
    options: RecordingOptions
  ): Promise<void> {
    const config = vscode.workspace.getConfiguration('codeDiary');
    const excludePatterns = config.get<string[]>('excludePatterns', []);

    // Check if file should be excluded
    if (this.gitUtils.shouldExcludeFile(change.filePath, excludePatterns)) {
      return;
    }

    // Get Git user info
    const gitUser = await this.gitUtils.getGitUser();
    const commitHash = await this.gitUtils.getCurrentCommitHash();

    // Parse diff context if requested
    let context: any = {};
    if (options.includeContext) {
      context = this.gitUtils.parseDiffContext(change.diff);
    }

    // Auto-detect change type if requested
    let changeType = change.changeType;
    if (options.autoDetectChangeType) {
      changeType = this.detectChangeType(change.diff);
    }

    // Prompt for rationale
    let rationale = '';
    if (options.promptForRationale !== false) {
      rationale = await this.promptForRationale(change.filePath, changeType);
      if (!rationale) {
        return; // User cancelled
      }
    }

    // Prompt for tags
    const tags = await this.promptForTags();

    // Create diary entry
    const entry: DiaryEntry = {
      timestamp: new Date().toISOString(),
      filePath: change.filePath,
      commitHash,
      author: gitUser.name,
      changeType: changeType as any,
      codeDiff: change.diff,
      developerRationale: rationale,
      tags,
      lineNumber: context.lineNumbers?.[0],
      functionName: context.functions?.[0],
      className: context.classes?.[0]
    };

    // Save to diary
    await this.mcpClient.createEntry(entry);
    
    vscode.window.showInformationMessage(
      `Recorded change for ${change.filePath}`,
      'View Diary'
    ).then(action => {
      if (action === 'View Diary') {
        vscode.commands.executeCommand('codeDiary.showDiaryPanel');
      }
    });
  }

  /**
   * Prompt user for rationale input
   */
  private async promptForRationale(filePath: string, changeType: string): Promise<string> {
    const placeholder = this.getRationalePlaceholder(changeType);
    
    const rationale = await vscode.window.showInputBox({
      title: `Code Diary - ${filePath}`,
      prompt: `Why did you make this ${changeType.toLowerCase()} change?`,
      placeholder,
      validateInput: (value) => {
        if (!value || value.trim().length < 10) {
          return 'Please provide a meaningful explanation (at least 10 characters)';
        }
        return null;
      }
    });

    return rationale?.trim() || '';
  }

  /**
   * Prompt user for tags
   */
  private async promptForTags(): Promise<string[]> {
    const tagsInput = await vscode.window.showInputBox({
      prompt: 'Add tags (comma-separated, optional)',
      placeholder: 'e.g., bugfix, performance, refactor',
      validateInput: (value) => {
        if (value && value.includes(';')) {
          return 'Please use commas to separate tags, not semicolons';
        }
        return null;
      }
    });

    if (!tagsInput) {
      return [];
    }

    return tagsInput
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
  }

  /**
   * Get placeholder text for rationale based on change type
   */
  private getRationalePlaceholder(changeType: string): string {
    const placeholders = {
      'ADD': 'Describe what you added and why it was needed...',
      'MODIFY': 'Explain what you changed and the reason behind it...',
      'DELETE': 'Explain why this code was removed...',
      'REFACTOR': 'Describe how you improved the code structure...',
      'FIX': 'Explain what bug you fixed and how...',
      'FEATURE': 'Describe the new feature and its purpose...'
    };

    return placeholders[changeType] || 'Describe your changes and the reasoning...';
  }

  /**
   * Detect change type from diff content
   */
  private detectChangeType(diff: string): string {
    const lines = diff.split('\n');
    const addedLines = lines.filter(line => line.startsWith('+')).length;
    const removedLines = lines.filter(line => line.startsWith('-')).length;

    // Simple heuristics for change type detection
    if (addedLines > 0 && removedLines === 0) {
      return 'ADD';
    } else if (addedLines === 0 && removedLines > 0) {
      return 'DELETE';
    } else if (addedLines > removedLines * 2) {
      return 'FEATURE';
    } else if (removedLines > addedLines * 2) {
      return 'REFACTOR';
    } else {
      // Look for keywords in the diff
      const diffContent = diff.toLowerCase();
      if (diffContent.includes('fix') || diffContent.includes('bug')) {
        return 'FIX';
      } else if (diffContent.includes('refactor') || diffContent.includes('cleanup')) {
        return 'REFACTOR';
      } else {
        return 'MODIFY';
      }
    }
  }

  /**
   * Summarize changes for display
   */
  private summarizeChanges(changes: GitChange[]): string {
    const summary = changes.map(change => 
      `${change.changeType}: ${change.filePath}`
    ).join('\n');
    
    return summary;
  }

  /**
   * Dispose of event listeners
   */
  dispose(): void {
    this.disposables.forEach(disposable => disposable.dispose());
    this.disposables = [];
  }
}
