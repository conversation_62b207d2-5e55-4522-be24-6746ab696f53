{"name": "code-diary", "displayName": "Code Diary", "description": "Automatically capture and query code change rationale with Augment AI", "version": "1.0.0", "publisher": "your-publisher-name", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "SCM Providers"], "keywords": ["git", "diary", "augment", "ai", "code-analysis"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "codeDiary.recordChange", "title": "Record Code Change", "category": "Code Diary"}, {"command": "codeDiary.viewHistory", "title": "View File History", "category": "Code Diary"}, {"command": "codeDiary.queryWithAugment", "title": "Query with Augment", "category": "Code Diary"}, {"command": "codeDiary.showDiaryPanel", "title": "Show Diary Panel", "category": "Code Diary"}], "menus": {"editor/context": [{"command": "codeDiary.recordChange", "when": "editorHasSelection", "group": "codeDiary"}, {"command": "codeDiary.viewHistory", "group": "codeDiary"}, {"command": "codeDiary.queryWithAugment", "group": "codeDiary"}], "explorer/context": [{"command": "codeDiary.viewHistory", "when": "resourceExtname =~ /\\.(js|ts|jsx|tsx|py|java|cpp|c|h|cs|php|rb|go|rs)$/", "group": "codeDiary"}], "scm/title": [{"command": "codeDiary.recordChange", "when": "scmProvider == git", "group": "navigation"}]}, "configuration": {"title": "Code Diary", "properties": {"codeDiary.mcpServerPath": {"type": "string", "default": "", "description": "Path to the Code Diary MCP server executable"}, "codeDiary.databasePath": {"type": "string", "default": "./diary.db", "description": "Path to the SQLite database file"}, "codeDiary.autoRecordOnCommit": {"type": "boolean", "default": true, "description": "Automatically prompt for rationale when committing changes"}, "codeDiary.promptForMinorChanges": {"type": "boolean", "default": false, "description": "Prompt for rationale even for minor changes"}, "codeDiary.excludePatterns": {"type": "array", "items": {"type": "string"}, "default": ["node_modules/**", "dist/**", "build/**", "*.log", "*.tmp"], "description": "File patterns to exclude from diary recording"}}}, "views": {"explorer": [{"id": "codeDiaryExplorer", "name": "Code Diary", "when": "codeDiary.enabled"}]}, "viewsContainers": {"activitybar": [{"id": "codeDiary", "title": "Code Diary", "icon": "$(book)"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "typescript": "^5.0.0", "@vscode/test-electron": "^2.3.0", "@vscode/vsce": "^2.19.0"}, "dependencies": {"simple-git": "^3.19.0", "axios": "^1.5.0", "minimatch": "^9.0.0"}}