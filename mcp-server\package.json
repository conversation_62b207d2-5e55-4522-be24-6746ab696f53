{"name": "code-diary-mcp-server", "version": "1.0.0", "description": "MCP Server for Code Diary - stores and retrieves code change information", "main": "dist/server.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "start": "node dist/server.js", "test": "jest", "clean": "rm -rf dist"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "sqlite3": "^5.1.6", "commander": "^11.0.0", "zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/sqlite3": "^3.1.8", "@types/jest": "^29.5.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["mcp", "server", "code-diary", "git", "vscode"], "author": "Your Name", "license": "MIT", "jest": {"preset": "ts-jest", "testEnvironment": "node", "extensionsToTreatAsEsm": [".ts"], "globals": {"ts-jest": {"useESM": true}}}}