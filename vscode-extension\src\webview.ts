import * as vscode from 'vscode';
import { <PERSON><PERSON><PERSON>, DiaryEntry, SearchQuery } from './mcpClient';

export class Diary<PERSON>ebviewProvider implements vscode.WebviewViewProvider {
  public static readonly viewType = 'codeDiaryExplorer';

  private _view?: vscode.WebviewView;
  private mcpClient: MCPClient;

  constructor(
    private readonly _extensionUri: vscode.Uri,
    mcpClient: MCPClient
  ) {
    this.mcpClient = mcpClient;
  }

  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken,
  ) {
    this._view = webviewView;

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [this._extensionUri]
    };

    webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

    // Handle messages from the webview
    webviewView.webview.onDidReceiveMessage(
      message => this.handleWebviewMessage(message),
      undefined,
      []
    );

    // Load initial data
    this.loadRecentEntries();
  }

  private async handleWebviewMessage(message: any) {
    switch (message.type) {
      case 'search':
        await this.handleSearch(message.query);
        break;
      case 'viewEntry':
        await this.handleViewEntry(message.entryId);
        break;
      case 'openFile':
        await this.handleOpenFile(message.filePath, message.lineNumber);
        break;
      case 'queryAugment':
        await this.handleQueryAugment(message.query);
        break;
      case 'refresh':
        await this.loadRecentEntries();
        break;
    }
  }

  private async handleSearch(query: SearchQuery) {
    try {
      const result = await this.mcpClient.searchEntries(query);
      this.sendMessage({
        type: 'searchResults',
        data: result
      });
    } catch (error) {
      this.sendMessage({
        type: 'error',
        message: `Search failed: ${error}`
      });
    }
  }

  private async handleViewEntry(entryId: string) {
    try {
      // In a real implementation, you'd fetch the specific entry
      // For now, we'll show a placeholder
      this.sendMessage({
        type: 'entryDetails',
        data: { id: entryId, message: 'Entry details would be shown here' }
      });
    } catch (error) {
      this.sendMessage({
        type: 'error',
        message: `Failed to load entry: ${error}`
      });
    }
  }

  private async handleOpenFile(filePath: string, lineNumber?: number) {
    try {
      const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
      if (!workspaceFolder) {
        throw new Error('No workspace folder found');
      }

      const fileUri = vscode.Uri.joinPath(workspaceFolder.uri, filePath);
      const document = await vscode.workspace.openTextDocument(fileUri);
      const editor = await vscode.window.showTextDocument(document);

      if (lineNumber) {
        const position = new vscode.Position(lineNumber - 1, 0);
        editor.selection = new vscode.Selection(position, position);
        editor.revealRange(new vscode.Range(position, position));
      }
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to open file: ${error}`);
    }
  }

  private async handleQueryAugment(query: string) {
    try {
      // Open Augment Chat with pre-filled query
      const augmentQuery = `Code Diary: ${query}`;
      
      // Try to open Augment Chat (this would depend on Augment's API)
      vscode.window.showInformationMessage(
        `Would open Augment Chat with query: "${augmentQuery}"`,
        'Copy Query'
      ).then(action => {
        if (action === 'Copy Query') {
          vscode.env.clipboard.writeText(augmentQuery);
        }
      });
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to query Augment: ${error}`);
    }
  }

  private async loadRecentEntries() {
    try {
      const result = await this.mcpClient.searchEntries({
        limit: 20,
        offset: 0
      });

      this.sendMessage({
        type: 'recentEntries',
        data: result.entries
      });
    } catch (error) {
      this.sendMessage({
        type: 'error',
        message: `Failed to load recent entries: ${error}`
      });
    }
  }

  private sendMessage(message: any) {
    if (this._view) {
      this._view.webview.postMessage(message);
    }
  }

  private _getHtmlForWebview(webview: vscode.Webview) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Diary</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 10px;
        }
        
        .search-container {
            margin-bottom: 15px;
        }
        
        .search-input {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 3px;
        }
        
        .search-button {
            margin-top: 5px;
            padding: 6px 12px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .search-button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        .entry {
            border: 1px solid var(--vscode-panel-border);
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            background-color: var(--vscode-editor-background);
        }
        
        .entry-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .entry-file {
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
            cursor: pointer;
        }
        
        .entry-file:hover {
            text-decoration: underline;
        }
        
        .entry-type {
            background-color: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8em;
        }
        
        .entry-rationale {
            margin: 8px 0;
            font-style: italic;
        }
        
        .entry-meta {
            font-size: 0.8em;
            color: var(--vscode-descriptionForeground);
            display: flex;
            justify-content: space-between;
        }
        
        .entry-actions {
            margin-top: 8px;
        }
        
        .action-button {
            padding: 4px 8px;
            margin-right: 5px;
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8em;
        }
        
        .action-button:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }
        
        .loading {
            text-align: center;
            color: var(--vscode-descriptionForeground);
            padding: 20px;
        }
        
        .error {
            color: var(--vscode-errorForeground);
            background-color: var(--vscode-inputValidation-errorBackground);
            border: 1px solid var(--vscode-inputValidation-errorBorder);
            padding: 8px;
            border-radius: 3px;
            margin-bottom: 10px;
        }
        
        .tags {
            margin-top: 5px;
        }
        
        .tag {
            background-color: var(--vscode-textBlockQuote-background);
            color: var(--vscode-textBlockQuote-foreground);
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 0.7em;
            margin-right: 3px;
        }
    </style>
</head>
<body>
    <div class="search-container">
        <input type="text" id="searchInput" class="search-input" placeholder="Search diary entries...">
        <button id="searchButton" class="search-button">Search</button>
        <button id="refreshButton" class="search-button">Refresh</button>
    </div>
    
    <div id="content">
        <div class="loading">Loading recent entries...</div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        
        document.getElementById('searchButton').addEventListener('click', () => {
            const query = document.getElementById('searchInput').value;
            vscode.postMessage({
                type: 'search',
                query: { keyword: query, limit: 20 }
            });
        });
        
        document.getElementById('refreshButton').addEventListener('click', () => {
            vscode.postMessage({ type: 'refresh' });
        });
        
        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('searchButton').click();
            }
        });
        
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.type) {
                case 'recentEntries':
                case 'searchResults':
                    displayEntries(message.data);
                    break;
                case 'error':
                    displayError(message.message);
                    break;
            }
        });
        
        function displayEntries(entries) {
            const content = document.getElementById('content');
            
            if (!entries || entries.length === 0) {
                content.innerHTML = '<div class="loading">No entries found</div>';
                return;
            }
            
            const entriesHtml = entries.map(entry => createEntryHtml(entry)).join('');
            content.innerHTML = entriesHtml;
        }
        
        function createEntryHtml(entry) {
            const tags = entry.tags ? entry.tags.map(tag => 
                \`<span class="tag">\${tag}</span>\`
            ).join('') : '';
            
            return \`
                <div class="entry">
                    <div class="entry-header">
                        <span class="entry-file" onclick="openFile('\${entry.filePath}', \${entry.lineNumber || 'null'})">\${entry.filePath}</span>
                        <span class="entry-type">\${entry.changeType}</span>
                    </div>
                    <div class="entry-rationale">\${entry.developerRationale}</div>
                    <div class="entry-meta">
                        <span>\${entry.author}</span>
                        <span>\${new Date(entry.timestamp).toLocaleDateString()}</span>
                    </div>
                    \${tags ? \`<div class="tags">\${tags}</div>\` : ''}
                    <div class="entry-actions">
                        <button class="action-button" onclick="viewEntry('\${entry.id}')">Details</button>
                        <button class="action-button" onclick="queryAugment('Why was \${entry.filePath} changed?')">Ask Augment</button>
                    </div>
                </div>
            \`;
        }
        
        function displayError(message) {
            const content = document.getElementById('content');
            content.innerHTML = \`<div class="error">\${message}</div>\`;
        }
        
        function openFile(filePath, lineNumber) {
            vscode.postMessage({
                type: 'openFile',
                filePath: filePath,
                lineNumber: lineNumber
            });
        }
        
        function viewEntry(entryId) {
            vscode.postMessage({
                type: 'viewEntry',
                entryId: entryId
            });
        }
        
        function queryAugment(query) {
            vscode.postMessage({
                type: 'queryAugment',
                query: query
            });
        }
    </script>
</body>
</html>`;
  }
}
