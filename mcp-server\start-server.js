#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Set the working directory to the mcp-server folder
const serverDir = __dirname;
const serverScript = path.join(serverDir, 'dist', 'server.js');
const dbPath = path.join(path.dirname(serverDir), 'diary.db');

// Start the MCP server
const server = spawn('node', [serverScript, '--db-path', dbPath], {
  cwd: serverDir,
  stdio: 'inherit'
});

// Handle server exit
server.on('close', (code) => {
  process.exit(code);
});

// Handle process termination
process.on('SIGINT', () => {
  server.kill('SIGINT');
});

process.on('SIGTERM', () => {
  server.kill('SIGTERM');
});
