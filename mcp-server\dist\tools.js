export const TOOLS = [
    {
        name: 'create_diary_entry',
        description: 'Create a new diary entry for a code change with rich context',
        inputSchema: {
            type: 'object',
            properties: {
                entry: {
                    type: 'object',
                    properties: {
                        timestamp: {
                            type: 'string',
                            description: 'ISO timestamp of when the change was made'
                        },
                        filePath: {
                            type: 'string',
                            description: 'Path to the file that was changed'
                        },
                        commitHash: {
                            type: 'string',
                            description: 'Git commit hash (optional)'
                        },
                        author: {
                            type: 'string',
                            description: 'Author of the change'
                        },
                        changeType: {
                            type: 'string',
                            enum: ['ADD', 'MODIFY', 'DELETE', 'REFACTOR', 'FIX', 'FEATURE'],
                            description: 'Type of change made'
                        },
                        codeDiff: {
                            type: 'string',
                            description: 'The actual code diff'
                        },
                        developerRationale: {
                            type: 'string',
                            description: 'Developer explanation of why the change was made'
                        },
                        aiSummary: {
                            type: 'string',
                            description: 'AI-generated summary (optional)'
                        },
                        aiImpactAnalysis: {
                            type: 'string',
                            description: 'AI-generated impact analysis (optional)'
                        },
                        tags: {
                            type: 'array',
                            items: { type: 'string' },
                            description: 'Tags for categorization'
                        },
                        lineNumber: {
                            type: 'number',
                            description: 'Specific line number affected (optional)'
                        },
                        functionName: {
                            type: 'string',
                            description: 'Function name affected (optional)'
                        },
                        className: {
                            type: 'string',
                            description: 'Class name affected (optional)'
                        },
                        // Enhanced context fields for tremendous value
                        businessContext: {
                            type: 'string',
                            description: 'Why this change matters to the business/product (optional)'
                        },
                        technicalDebt: {
                            type: 'string',
                            description: 'Trade-offs made, technical debt incurred (optional)'
                        },
                        alternativesConsidered: {
                            type: 'string',
                            description: 'Other approaches considered and why they were rejected (optional)'
                        },
                        futureConsiderations: {
                            type: 'string',
                            description: 'What to watch out for, future improvements needed (optional)'
                        },
                        relatedTickets: {
                            type: 'array',
                            items: { type: 'string' },
                            description: 'Related JIRA/GitHub issues (optional)'
                        },
                        reviewComments: {
                            type: 'string',
                            description: 'Key insights from code review (optional)'
                        },
                        performanceImpact: {
                            type: 'string',
                            description: 'Performance considerations and impact (optional)'
                        },
                        securityImpact: {
                            type: 'string',
                            description: 'Security implications (optional)'
                        },
                        testingStrategy: {
                            type: 'string',
                            description: 'How this change was tested (optional)'
                        },
                        rollbackPlan: {
                            type: 'string',
                            description: 'How to undo this change if needed (optional)'
                        },
                        dependencies: {
                            type: 'array',
                            items: { type: 'string' },
                            description: 'What this change depends on (optional)'
                        },
                        affectedComponents: {
                            type: 'array',
                            items: { type: 'string' },
                            description: 'What components this change affects (optional)'
                        },
                        estimatedComplexity: {
                            type: 'string',
                            enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'],
                            description: 'Complexity level of the change (optional)'
                        },
                        urgencyLevel: {
                            type: 'string',
                            enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'],
                            description: 'Urgency level of the change (optional)'
                        },
                        knowledgeLevel: {
                            type: 'string',
                            enum: ['JUNIOR', 'MID', 'SENIOR', 'EXPERT'],
                            description: 'Author expertise level for this change (optional)'
                        }
                    },
                    required: ['timestamp', 'filePath', 'author', 'changeType', 'codeDiff', 'developerRationale']
                }
            },
            required: ['entry']
        }
    },
    {
        name: 'search_diary_entries',
        description: 'Search diary entries with various filters',
        inputSchema: {
            type: 'object',
            properties: {
                query: {
                    type: 'object',
                    properties: {
                        filePath: {
                            type: 'string',
                            description: 'Filter by file path (supports partial matches)'
                        },
                        author: {
                            type: 'string',
                            description: 'Filter by author'
                        },
                        changeType: {
                            type: 'string',
                            description: 'Filter by change type'
                        },
                        keyword: {
                            type: 'string',
                            description: 'Search in rationale, summary, and diff'
                        },
                        commitHash: {
                            type: 'string',
                            description: 'Filter by commit hash'
                        },
                        dateFrom: {
                            type: 'string',
                            description: 'Start date (ISO format)'
                        },
                        dateTo: {
                            type: 'string',
                            description: 'End date (ISO format)'
                        },
                        lineNumber: {
                            type: 'number',
                            description: 'Filter by line number'
                        },
                        limit: {
                            type: 'number',
                            default: 50,
                            description: 'Maximum number of results'
                        },
                        offset: {
                            type: 'number',
                            default: 0,
                            description: 'Offset for pagination'
                        }
                    }
                }
            },
            required: ['query']
        }
    },
    {
        name: 'get_file_history',
        description: 'Get the change history for a specific file',
        inputSchema: {
            type: 'object',
            properties: {
                filePath: {
                    type: 'string',
                    description: 'Path to the file'
                },
                lineNumber: {
                    type: 'number',
                    description: 'Specific line number (optional)'
                }
            },
            required: ['filePath']
        }
    },
    {
        name: 'get_commit_entries',
        description: 'Get all diary entries for a specific commit',
        inputSchema: {
            type: 'object',
            properties: {
                commitHash: {
                    type: 'string',
                    description: 'Git commit hash'
                }
            },
            required: ['commitHash']
        }
    },
    {
        name: 'search_by_intent',
        description: 'Search diary entries by developer intent or purpose',
        inputSchema: {
            type: 'object',
            properties: {
                intent: {
                    type: 'string',
                    description: 'The intent or purpose to search for (e.g., "fix authentication bug")'
                },
                limit: {
                    type: 'number',
                    default: 10,
                    description: 'Maximum number of results'
                }
            },
            required: ['intent']
        }
    },
    {
        name: 'get_evolution',
        description: 'Get the evolution timeline of a file, function, or class',
        inputSchema: {
            type: 'object',
            properties: {
                filePath: {
                    type: 'string',
                    description: 'Path to the file'
                },
                functionName: {
                    type: 'string',
                    description: 'Specific function name (optional)'
                },
                className: {
                    type: 'string',
                    description: 'Specific class name (optional)'
                }
            },
            required: ['filePath']
        }
    },
    {
        name: 'get_diary_entry',
        description: 'Get a specific diary entry by ID',
        inputSchema: {
            type: 'object',
            properties: {
                id: {
                    type: 'string',
                    description: 'Diary entry ID'
                }
            },
            required: ['id']
        }
    },
    {
        name: 'correlate_changes',
        description: 'Find related changes made around the same time',
        inputSchema: {
            type: 'object',
            properties: {
                timestamp: {
                    type: 'string',
                    description: 'Reference timestamp (ISO format)'
                },
                timeWindow: {
                    type: 'number',
                    default: 3600,
                    description: 'Time window in seconds (default: 1 hour)'
                },
                excludeAuthor: {
                    type: 'string',
                    description: 'Exclude changes by this author (optional)'
                }
            },
            required: ['timestamp']
        }
    },
    // NEW TOOLS FOR TREMENDOUS VALUE
    {
        name: 'get_onboarding_context',
        description: 'Get comprehensive context for onboarding new developers to a file/component',
        inputSchema: {
            type: 'object',
            properties: {
                filePath: {
                    type: 'string',
                    description: 'Path to the file or component'
                },
                includeRelated: {
                    type: 'boolean',
                    description: 'Include related files and dependencies',
                    default: true
                }
            },
            required: ['filePath']
        }
    },
    {
        name: 'analyze_technical_debt',
        description: 'Analyze technical debt patterns and trade-offs across the codebase',
        inputSchema: {
            type: 'object',
            properties: {
                filePath: {
                    type: 'string',
                    description: 'Specific file to analyze (optional)'
                },
                author: {
                    type: 'string',
                    description: 'Specific author to analyze (optional)'
                },
                timeRange: {
                    type: 'string',
                    description: 'Time range in days (e.g., "30" for last 30 days, optional)'
                }
            }
        }
    },
    {
        name: 'get_decision_rationale',
        description: 'Get the rationale behind specific code decisions and alternatives considered',
        inputSchema: {
            type: 'object',
            properties: {
                filePath: {
                    type: 'string',
                    description: 'Path to the file'
                },
                functionName: {
                    type: 'string',
                    description: 'Specific function (optional)'
                },
                className: {
                    type: 'string',
                    description: 'Specific class (optional)'
                },
                includeAlternatives: {
                    type: 'boolean',
                    description: 'Include alternatives that were considered',
                    default: true
                }
            },
            required: ['filePath']
        }
    },
    {
        name: 'get_risk_assessment',
        description: 'Get risk assessment for changes including rollback plans and impact analysis',
        inputSchema: {
            type: 'object',
            properties: {
                filePath: {
                    type: 'string',
                    description: 'Path to the file (optional)'
                },
                changeType: {
                    type: 'string',
                    enum: ['ADD', 'MODIFY', 'DELETE', 'REFACTOR', 'FIX', 'FEATURE'],
                    description: 'Type of change to analyze (optional)'
                },
                complexityLevel: {
                    type: 'string',
                    enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'],
                    description: 'Minimum complexity level (optional)'
                }
            }
        }
    },
    {
        name: 'get_knowledge_transfer',
        description: 'Get knowledge transfer information for when developers leave or join',
        inputSchema: {
            type: 'object',
            properties: {
                author: {
                    type: 'string',
                    description: 'Developer whose knowledge to transfer'
                },
                includeContext: {
                    type: 'boolean',
                    description: 'Include business context and rationale',
                    default: true
                },
                includeRisks: {
                    type: 'boolean',
                    description: 'Include risk assessments and rollback plans',
                    default: true
                }
            },
            required: ['author']
        }
    }
];
//# sourceMappingURL=tools.js.map