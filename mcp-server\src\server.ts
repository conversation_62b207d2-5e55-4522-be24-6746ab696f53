#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { Command } from 'commander';
import { DiaryDatabase } from './database.js';
import { TOOLS } from './tools.js';
import {
  DiaryEntrySchema,
  SearchQuerySchema,
  CreateEntryParams,
  SearchEntriesParams,
  GetEntryParams,
  GetFileHistoryParams,
  GetCommitEntriesParams,
  SearchByIntentParams,
  GetEvolutionParams,
  ValidationError,
  NotFoundError,
  DatabaseError
} from './types.js';

class CodeDiaryMCPServer {
  private server: Server;
  private database: DiaryDatabase;

  constructor(dbPath: string) {
    this.database = new DiaryDatabase(dbPath);
    this.server = new Server(
      {
        name: 'code-diary-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupHandlers();
  }

  private setupHandlers(): void {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: TOOLS,
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'create_diary_entry':
            return await this.handleCreateEntry(args as unknown as CreateEntryParams);

          case 'search_diary_entries':
            return await this.handleSearchEntries(args as unknown as SearchEntriesParams);

          case 'get_diary_entry':
            return await this.handleGetEntry(args as unknown as GetEntryParams);

          case 'get_file_history':
            return await this.handleGetFileHistory(args as unknown as GetFileHistoryParams);

          case 'get_commit_entries':
            return await this.handleGetCommitEntries(args as unknown as GetCommitEntriesParams);

          case 'search_by_intent':
            return await this.handleSearchByIntent(args as unknown as SearchByIntentParams);

          case 'get_evolution':
            return await this.handleGetEvolution(args as unknown as GetEvolutionParams);

          case 'correlate_changes':
            return await this.handleCorrelateChanges(args);

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        if (error instanceof ValidationError || error instanceof NotFoundError) {
          return {
            content: [
              {
                type: 'text',
                text: `Error: ${error.message}`,
              },
            ],
            isError: true,
          };
        }

        console.error(`Error handling tool call ${name}:`, error);
        return {
          content: [
            {
              type: 'text',
              text: `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  private async handleCreateEntry(params: CreateEntryParams) {
    const validatedEntry = DiaryEntrySchema.parse(params.entry);
    const created = await this.database.createEntry(validatedEntry);

    return {
      content: [
        {
          type: 'text',
          text: `Created diary entry with ID: ${created.id}`,
        },
        {
          type: 'text',
          text: JSON.stringify(created, null, 2),
        },
      ],
    };
  }

  private async handleSearchEntries(params: SearchEntriesParams) {
    const validatedQuery = SearchQuerySchema.parse(params.query);
    const result = await this.database.searchEntries(validatedQuery);

    return {
      content: [
        {
          type: 'text',
          text: `Found ${result.total} entries (showing ${result.entries.length})`,
        },
        {
          type: 'text',
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  }

  private async handleGetEntry(params: GetEntryParams) {
    const entry = await this.database.getEntry(params.id);

    if (!entry) {
      throw new NotFoundError(`Diary entry with ID ${params.id} not found`);
    }

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(entry, null, 2),
        },
      ],
    };
  }

  private async handleGetFileHistory(params: GetFileHistoryParams) {
    const entries = await this.database.getFileHistory(params.filePath, params.lineNumber);

    return {
      content: [
        {
          type: 'text',
          text: `Found ${entries.length} entries for ${params.filePath}${params.lineNumber ? ` at line ${params.lineNumber}` : ''}`,
        },
        {
          type: 'text',
          text: JSON.stringify(entries, null, 2),
        },
      ],
    };
  }

  private async handleGetCommitEntries(params: GetCommitEntriesParams) {
    const entries = await this.database.getCommitEntries(params.commitHash);

    return {
      content: [
        {
          type: 'text',
          text: `Found ${entries.length} entries for commit ${params.commitHash}`,
        },
        {
          type: 'text',
          text: JSON.stringify(entries, null, 2),
        },
      ],
    };
  }

  private async handleSearchByIntent(params: SearchByIntentParams) {
    const entries = await this.database.searchByIntent(params.intent, params.limit);

    return {
      content: [
        {
          type: 'text',
          text: `Found ${entries.length} entries matching intent: "${params.intent}"`,
        },
        {
          type: 'text',
          text: JSON.stringify(entries, null, 2),
        },
      ],
    };
  }

  private async handleGetEvolution(params: GetEvolutionParams) {
    const result = await this.database.getEvolution(
      params.filePath,
      params.functionName,
      params.className
    );

    return {
      content: [
        {
          type: 'text',
          text: `Evolution of ${params.filePath}${params.functionName ? `::${params.functionName}` : ''}${params.className ? ` (${params.className})` : ''}`,
        },
        {
          type: 'text',
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  }

  private async handleCorrelateChanges(params: any) {
    const { timestamp, timeWindow = 3600, excludeAuthor } = params;

    // Calculate time range
    const refTime = new Date(timestamp);
    const startTime = new Date(refTime.getTime() - (timeWindow * 1000));
    const endTime = new Date(refTime.getTime() + (timeWindow * 1000));

    const query = {
      dateFrom: startTime.toISOString(),
      dateTo: endTime.toISOString(),
      limit: 50,
      offset: 0
    };

    const result = await this.database.searchEntries(query);

    // Filter out excluded author if specified
    let filteredEntries = result.entries;
    if (excludeAuthor) {
      filteredEntries = result.entries.filter(entry => entry.author !== excludeAuthor);
    }

    return {
      content: [
        {
          type: 'text',
          text: `Found ${filteredEntries.length} related changes within ${timeWindow} seconds of ${timestamp}`,
        },
        {
          type: 'text',
          text: JSON.stringify(filteredEntries, null, 2),
        },
      ],
    };
  }

  async start(): Promise<void> {
    await this.database.initialize();

    const transport = new StdioServerTransport();
    await this.server.connect(transport);

    console.error('Code Diary MCP Server running on stdio');
  }

  async stop(): Promise<void> {
    await this.database.close();
  }
}

// CLI setup
const program = new Command();

program
  .name('code-diary-mcp-server')
  .description('MCP Server for Code Diary')
  .version('1.0.0')
  .option('--db-path <path>', 'Path to SQLite database file', './diary.db')
  .action(async (options) => {
    const server = new CodeDiaryMCPServer(options.dbPath);

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      console.error('Shutting down...');
      await server.stop();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.error('Shutting down...');
      await server.stop();
      process.exit(0);
    });

    try {
      await server.start();

      // Keep the process alive
      process.stdin.resume();

    } catch (error) {
      console.error('Failed to start server:', error);
      process.exit(1);
    }
  });

// Always parse when this file is run directly
program.parse();
