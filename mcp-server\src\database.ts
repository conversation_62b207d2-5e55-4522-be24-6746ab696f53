import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import { 
  <PERSON><PERSON><PERSON>ry, 
  DiaryEntryRow, 
  SearchQuery, 
  SearchResult,
  EvolutionResult,
  DatabaseError,
  NotFoundError 
} from './types.js';

export class DiaryDatabase {
  private db: sqlite3.Database;
  private dbRun: (sql: string, params?: any[]) => Promise<sqlite3.RunResult>;
  private dbGet: (sql: string, params?: any[]) => Promise<any>;
  private dbAll: (sql: string, params?: any[]) => Promise<any[]>;

  constructor(dbPath: string) {
    this.db = new sqlite3.Database(dbPath);
    
    // Promisify database methods
    this.dbRun = promisify(this.db.run.bind(this.db));
    this.dbGet = promisify(this.db.get.bind(this.db));
    this.dbAll = promisify(this.db.all.bind(this.db));
  }

  async initialize(): Promise<void> {
    try {
      await this.dbRun(`
        CREATE TABLE IF NOT EXISTS diary_entries (
          id TEXT PRIMARY KEY,
          timestamp TEXT NOT NULL,
          filePath TEXT NOT NULL,
          commitHash TEXT,
          author TEXT NOT NULL,
          changeType TEXT NOT NULL,
          codeDiff TEXT NOT NULL,
          developerRationale TEXT NOT NULL,
          aiSummary TEXT,
          aiImpactAnalysis TEXT,
          tags TEXT, -- JSON array as string
          lineNumber INTEGER,
          functionName TEXT,
          className TEXT,
          createdAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create indexes for better query performance
      await this.dbRun('CREATE INDEX IF NOT EXISTS idx_file_path ON diary_entries(filePath)');
      await this.dbRun('CREATE INDEX IF NOT EXISTS idx_commit_hash ON diary_entries(commitHash)');
      await this.dbRun('CREATE INDEX IF NOT EXISTS idx_author ON diary_entries(author)');
      await this.dbRun('CREATE INDEX IF NOT EXISTS idx_timestamp ON diary_entries(timestamp)');
      await this.dbRun('CREATE INDEX IF NOT EXISTS idx_change_type ON diary_entries(changeType)');
      await this.dbRun('CREATE INDEX IF NOT EXISTS idx_line_number ON diary_entries(lineNumber)');

      console.log('Database initialized successfully');
    } catch (error) {
      throw new DatabaseError(`Failed to initialize database: ${error}`);
    }
  }

  async createEntry(entry: DiaryEntry): Promise<DiaryEntryRow> {
    try {
      const id = entry.id || this.generateId();
      const now = new Date().toISOString();
      const tags = JSON.stringify(entry.tags || []);

      await this.dbRun(`
        INSERT INTO diary_entries (
          id, timestamp, filePath, commitHash, author, changeType,
          codeDiff, developerRationale, aiSummary, aiImpactAnalysis,
          tags, lineNumber, functionName, className, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        id, entry.timestamp, entry.filePath, entry.commitHash, entry.author,
        entry.changeType, entry.codeDiff, entry.developerRationale,
        entry.aiSummary, entry.aiImpactAnalysis, tags, entry.lineNumber,
        entry.functionName, entry.className, now, now
      ]);

      const created = await this.getEntry(id);
      if (!created) {
        throw new DatabaseError('Failed to retrieve created entry');
      }

      return created;
    } catch (error) {
      throw new DatabaseError(`Failed to create diary entry: ${error}`);
    }
  }

  async getEntry(id: string): Promise<DiaryEntryRow | null> {
    try {
      const row = await this.dbGet(
        'SELECT * FROM diary_entries WHERE id = ?',
        [id]
      );

      return row ? this.rowToEntry(row) : null;
    } catch (error) {
      throw new DatabaseError(`Failed to get diary entry: ${error}`);
    }
  }

  async searchEntries(query: SearchQuery): Promise<SearchResult> {
    try {
      const { whereClause, params } = this.buildWhereClause(query);
      
      // Get total count
      const countSql = `SELECT COUNT(*) as total FROM diary_entries ${whereClause}`;
      const countResult = await this.dbGet(countSql, params);
      const total = countResult.total;

      // Get entries with pagination
      const sql = `
        SELECT * FROM diary_entries 
        ${whereClause}
        ORDER BY timestamp DESC 
        LIMIT ? OFFSET ?
      `;
      const rows = await this.dbAll(sql, [...params, query.limit, query.offset]);

      const entries = rows.map(row => this.rowToEntry(row));
      const hasMore = query.offset + query.limit < total;

      return { entries, total, hasMore };
    } catch (error) {
      throw new DatabaseError(`Failed to search diary entries: ${error}`);
    }
  }

  async getFileHistory(filePath: string, lineNumber?: number): Promise<DiaryEntryRow[]> {
    try {
      let sql = 'SELECT * FROM diary_entries WHERE filePath = ?';
      const params: any[] = [filePath];

      if (lineNumber !== undefined) {
        sql += ' AND lineNumber = ?';
        params.push(lineNumber);
      }

      sql += ' ORDER BY timestamp DESC';

      const rows = await this.dbAll(sql, params);
      return rows.map(row => this.rowToEntry(row));
    } catch (error) {
      throw new DatabaseError(`Failed to get file history: ${error}`);
    }
  }

  async getCommitEntries(commitHash: string): Promise<DiaryEntryRow[]> {
    try {
      const rows = await this.dbAll(
        'SELECT * FROM diary_entries WHERE commitHash = ? ORDER BY timestamp DESC',
        [commitHash]
      );
      return rows.map(row => this.rowToEntry(row));
    } catch (error) {
      throw new DatabaseError(`Failed to get commit entries: ${error}`);
    }
  }

  async searchByIntent(intent: string, limit: number = 10): Promise<DiaryEntryRow[]> {
    try {
      const rows = await this.dbAll(`
        SELECT * FROM diary_entries 
        WHERE developerRationale LIKE ? OR aiSummary LIKE ? OR tags LIKE ?
        ORDER BY timestamp DESC 
        LIMIT ?
      `, [`%${intent}%`, `%${intent}%`, `%${intent}%`, limit]);

      return rows.map(row => this.rowToEntry(row));
    } catch (error) {
      throw new DatabaseError(`Failed to search by intent: ${error}`);
    }
  }

  async getEvolution(filePath: string, functionName?: string, className?: string): Promise<EvolutionResult> {
    try {
      let sql = 'SELECT * FROM diary_entries WHERE filePath = ?';
      const params: any[] = [filePath];

      if (functionName) {
        sql += ' AND functionName = ?';
        params.push(functionName);
      }

      if (className) {
        sql += ' AND className = ?';
        params.push(className);
      }

      sql += ' ORDER BY timestamp ASC';

      const rows = await this.dbAll(sql, params);
      const entries = rows.map(row => this.rowToEntry(row));

      // Create timeline
      const timeline = this.createTimeline(entries);

      return { entries, timeline };
    } catch (error) {
      throw new DatabaseError(`Failed to get evolution: ${error}`);
    }
  }

  async close(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db.close((err) => {
        if (err) {
          reject(new DatabaseError(`Failed to close database: ${err}`));
        } else {
          resolve();
        }
      });
    });
  }

  private rowToEntry(row: any): DiaryEntryRow {
    return {
      ...row,
      tags: JSON.parse(row.tags || '[]')
    };
  }

  private buildWhereClause(query: SearchQuery): { whereClause: string; params: any[] } {
    const conditions: string[] = [];
    const params: any[] = [];

    if (query.filePath) {
      conditions.push('filePath LIKE ?');
      params.push(`%${query.filePath}%`);
    }

    if (query.author) {
      conditions.push('author = ?');
      params.push(query.author);
    }

    if (query.changeType) {
      conditions.push('changeType = ?');
      params.push(query.changeType);
    }

    if (query.commitHash) {
      conditions.push('commitHash = ?');
      params.push(query.commitHash);
    }

    if (query.keyword) {
      conditions.push('(developerRationale LIKE ? OR aiSummary LIKE ? OR codeDiff LIKE ?)');
      params.push(`%${query.keyword}%`, `%${query.keyword}%`, `%${query.keyword}%`);
    }

    if (query.dateFrom) {
      conditions.push('timestamp >= ?');
      params.push(query.dateFrom);
    }

    if (query.dateTo) {
      conditions.push('timestamp <= ?');
      params.push(query.dateTo);
    }

    if (query.lineNumber) {
      conditions.push('lineNumber = ?');
      params.push(query.lineNumber);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    return { whereClause, params };
  }

  private createTimeline(entries: DiaryEntryRow[]): EvolutionResult['timeline'] {
    const timelineMap = new Map<string, { changeCount: number; majorChanges: string[] }>();

    entries.forEach(entry => {
      const date = entry.timestamp.split('T')[0]; // Get date part
      const existing = timelineMap.get(date) || { changeCount: 0, majorChanges: [] };
      
      existing.changeCount++;
      if (['REFACTOR', 'FEATURE', 'FIX'].includes(entry.changeType)) {
        existing.majorChanges.push(`${entry.changeType}: ${entry.developerRationale.substring(0, 50)}...`);
      }

      timelineMap.set(date, existing);
    });

    return Array.from(timelineMap.entries()).map(([date, data]) => ({
      date,
      ...data
    }));
  }

  private generateId(): string {
    return `diary_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
