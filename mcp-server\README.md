# Code Diary MCP Server

A Model Context Protocol (MCP) server that stores and retrieves detailed code change information for the Code Diary VSCode extension.

## Features

- **Persistent Storage**: SQLite database for storing diary entries
- **Rich Querying**: Search by file, author, change type, intent, and more
- **Evolution Tracking**: Track how code evolves over time
- **Change Correlation**: Find related changes made around the same time
- **MCP Compliance**: Full compatibility with Augment Code platform

## Installation

```bash
npm install
npm run build
```

## Usage

### Start the Server

```bash
npm start -- --db-path ./diary.db
```

### Development Mode

```bash
npm run dev
```

## MCP Tools

The server exposes the following tools for Augment to use:

### `create_diary_entry`
Create a new diary entry for a code change.

**Parameters:**
- `entry`: DiaryEntry object with change details

### `search_diary_entries`
Search diary entries with various filters.

**Parameters:**
- `query`: SearchQuery object with filter criteria

### `get_file_history`
Get the change history for a specific file.

**Parameters:**
- `filePath`: Path to the file
- `lineNumber`: (optional) Specific line number

### `get_commit_entries`
Get all diary entries for a specific commit.

**Parameters:**
- `commitHash`: Git commit hash

### `search_by_intent`
Search diary entries by developer intent or purpose.

**Parameters:**
- `intent`: The intent to search for
- `limit`: (optional) Maximum results

### `get_evolution`
Get the evolution timeline of a file, function, or class.

**Parameters:**
- `filePath`: Path to the file
- `functionName`: (optional) Specific function
- `className`: (optional) Specific class

### `correlate_changes`
Find related changes made around the same time.

**Parameters:**
- `timestamp`: Reference timestamp
- `timeWindow`: (optional) Time window in seconds
- `excludeAuthor`: (optional) Author to exclude

## Database Schema

The server uses SQLite with the following schema:

```sql
CREATE TABLE diary_entries (
  id TEXT PRIMARY KEY,
  timestamp TEXT NOT NULL,
  filePath TEXT NOT NULL,
  commitHash TEXT,
  author TEXT NOT NULL,
  changeType TEXT NOT NULL,
  codeDiff TEXT NOT NULL,
  developerRationale TEXT NOT NULL,
  aiSummary TEXT,
  aiImpactAnalysis TEXT,
  tags TEXT, -- JSON array
  lineNumber INTEGER,
  functionName TEXT,
  className TEXT,
  createdAt TEXT NOT NULL,
  updatedAt TEXT NOT NULL
);
```

## Configuration with Augment

Add this server to your Augment settings:

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "CodeDiary",
        "command": "node",
        "args": ["./mcp-server/dist/server.js", "--db-path", "./diary.db"]
      }
    ]
  }
}
```

## API Examples

### Creating an Entry

```typescript
const entry = {
  timestamp: "2024-01-15T10:30:00Z",
  filePath: "src/auth.ts",
  author: "john.doe",
  changeType: "FIX",
  codeDiff: "- if (user.isValid) {\n+ if (user && user.isValid) {",
  developerRationale: "Added null check to prevent runtime error when user is undefined",
  tags: ["bugfix", "authentication"]
};
```

### Searching Entries

```typescript
const query = {
  filePath: "src/auth.ts",
  changeType: "FIX",
  dateFrom: "2024-01-01T00:00:00Z",
  limit: 10
};
```

## Error Handling

The server provides detailed error messages for:
- Validation errors (400)
- Not found errors (404)
- Database errors (500)

## Development

### Running Tests

```bash
npm test
```

### Building

```bash
npm run build
```

### Cleaning

```bash
npm run clean
```

## License

MIT
