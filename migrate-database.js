#!/usr/bin/env node

// Migration script to add enhanced context fields to existing database

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'diary.db');

console.log('🔄 Migrating Code Diary Database to Enhanced Schema\n');

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    return;
  }
  console.log('✅ Connected to the SQLite database.\n');
});

// Add new columns for enhanced context
const migrations = [
  'ALTER TABLE diary_entries ADD COLUMN businessContext TEXT',
  'ALTER TABLE diary_entries ADD COLUMN technicalDebt TEXT',
  'ALTER TABLE diary_entries ADD COLUMN alternativesConsidered TEXT',
  'ALTER TABLE diary_entries ADD COLUMN futureConsiderations TEXT',
  'ALTER TABLE diary_entries ADD COLUMN relatedTickets TEXT',
  'ALTER TABLE diary_entries ADD COLUMN reviewComments TEXT',
  'ALTER TABLE diary_entries ADD COLUMN performanceImpact TEXT',
  'ALTER TABLE diary_entries ADD COLUMN securityImpact TEXT',
  'ALTER TABLE diary_entries ADD COLUMN testingStrategy TEXT',
  'ALTER TABLE diary_entries ADD COLUMN rollbackPlan TEXT',
  'ALTER TABLE diary_entries ADD COLUMN dependencies TEXT',
  'ALTER TABLE diary_entries ADD COLUMN affectedComponents TEXT',
  'ALTER TABLE diary_entries ADD COLUMN estimatedComplexity TEXT',
  'ALTER TABLE diary_entries ADD COLUMN urgencyLevel TEXT',
  'ALTER TABLE diary_entries ADD COLUMN knowledgeLevel TEXT'
];

console.log('📝 Adding enhanced context columns...\n');

let completed = 0;
const total = migrations.length;

migrations.forEach((migration, index) => {
  db.run(migration, function(err) {
    completed++;
    
    if (err) {
      // Column might already exist, that's okay
      if (err.message.includes('duplicate column name')) {
        console.log(`⚠️  Column already exists: ${migration.split(' ')[5]}`);
      } else {
        console.log(`❌ Error: ${err.message}`);
      }
    } else {
      console.log(`✅ Added column: ${migration.split(' ')[5]}`);
    }
    
    if (completed === total) {
      console.log('\n🎉 Database migration completed!\n');
      
      // Verify the schema
      db.all("PRAGMA table_info(diary_entries)", [], (err, rows) => {
        if (err) {
          console.error('❌ Error checking schema:', err.message);
        } else {
          console.log('📊 Updated database schema:');
          rows.forEach(row => {
            console.log(`   ${row.name}: ${row.type}`);
          });
          console.log('\n🚀 Ready for enhanced diary entries!\n');
        }
        db.close();
      });
    }
  });
});
