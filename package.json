{"name": "code-diary-workspace", "version": "1.0.0", "description": "Augment-Powered Code Diary for VSCode - Workspace", "private": true, "workspaces": ["mcp-server", "vscode-extension"], "scripts": {"install:all": "npm install && npm run install:mcp && npm run install:extension", "install:mcp": "cd mcp-server && npm install", "install:extension": "cd vscode-extension && npm install", "build:all": "npm run build:mcp && npm run build:extension", "build:mcp": "cd mcp-server && npm run build", "build:extension": "cd vscode-extension && npm run compile", "dev:mcp": "cd mcp-server && npm run dev", "test:all": "npm run test:mcp && npm run test:extension", "test:mcp": "cd mcp-server && npm test", "test:extension": "cd vscode-extension && npm test"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/code-diary.git"}, "keywords": ["vscode", "extension", "mcp", "augment", "code-diary", "git", "ai"], "author": "Your Name", "license": "MIT"}